.hero-small {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    padding: 60px 0;
    text-align: center;
    color: #fff;
    font-family: 'Montser<PERSON>', 'Segoe UI', Aria<PERSON>, sans-serif;
    font-weight: 700;
    letter-spacing: 1px;
    /* Glass effect */
    background: rgba(30, 34, 90, 0.35);
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    border-radius: 20px;
    border: 1px solid rgba(255,255,255,0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
}

.hero-small h1 {
    margin-bottom: 15px;
    font-size: 2.8rem;
    font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
    font-weight: 900;
    background: linear-gradient(90deg, #ff8a00 10%, #e52e71 50%, #2575fc 90%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    text-shadow: 0 4px 16px rgba(37,117,252,0.25), 0 2px 8px rgba(229,46,113,0.18), 0 1px 2px rgba(0,0,0,0.10);
    letter-spacing: 2px;
    animation: heroTitleFadeIn 1s cubic-bezier(0.4,0,0.2,1);
}

@keyframes heroTitleFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-small p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.prompts-grid {
    padding: var(--section-padding);
}

.filters {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.filter-button {
    padding: 8px 20px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    background: var(--white);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-button:hover,
.filter-button.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.prompt-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.prompt-card {
    background: var(--white);
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.prompt-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.prompt-category {
    display: inline-block;
    padding: 4px 12px;
    background: var(--surface-color);
    border-radius: 15px;
    font-size: 0.85rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.prompt-card h2 {
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.prompt-description {
    color: var(--muted-text-color);
    font-size: 0.95rem;
    margin-bottom: 20px;
}

.prompt-text {
    background: var(--surface-color);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-family: monospace;
    font-size: 0.9rem;
}

.copy-prompt-button {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--primary-color);
    border-radius: 6px;
    background: transparent;
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.copy-prompt-button:hover {
    background: var(--primary-color);
    color: var(--white);
}

.copy-prompt-button.copy-animation::before {
    content: 'Copied!';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--secondary-color);
    color: var(--white);
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
    opacity: 0;
    animation: copyFeedback 1.5s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@keyframes copyFeedback {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.7);
    }
    15% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1);
    }
    25% {
        transform: translate(-50%, -50%) scale(1);
    }
    75% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.7);
    }
}
.copy-prompt-button.copied {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white);
}

.cta-section {
    text-align: center;
}

.cta-section h2 {
    margin-bottom: 15px;
}

.cta-section p {
    margin-bottom: 30px;
    color: var(--muted-text-color);
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-small {
        padding: 40px 0;
    }

    .filters {
        margin-bottom: 30px;
    }

    .prompt-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .prompt-card h2 {
        font-size: 1.2rem;
    }
}