/* Main App Layout */
:root {
  --primary-color: #6a11cb;
  --secondary-color: #2575fc;
  --gradient-start: var(--primary-color);
  --gradient-end: var(--secondary-color);
  --background-color: #ffffff;
  --surface-color: #f8f9fa;
  --text-color: #343a40;
  --heading-color: #212529;
  --muted-text-color: #6c757d;
  --border-color: #dee2e6;
  --white: #fff;
  --black: #000;
  --section-padding: 60px 0;
}

Typography */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  color: var(--heading-color);
  font-weight: 700;
  line-height: 1.2;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

h2 {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
  line-height: 1.6;
}

/* Button styles */
.cta-button {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.cta-button.primary {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: white;
  box-shadow: 0 4px 15px rgba(106, 17, 203, 0.4);
}

.cta-button.secondary {
  background-color: white;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.cta-button.large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

.cta-button:hover {
  transform: translateY(-3px);
}

.cta-button.primary:hover {
  box-shadow: 0 6px 20px rgba(106, 17, 203, 0.6);
}

.cta-button.secondary:hover {
  background-color: rgba(106, 17, 203, 0.05);
}

/* Animation classes */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.75rem;
  }
  
  h3 {
    font-size: 1.25rem;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .section-padding {
    padding: 40px 0;
  }
}
