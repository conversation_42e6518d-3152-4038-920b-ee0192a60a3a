.hero-small {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  padding: 60px 0;
  text-align: center;
  color: #fff;
  font-family: 'Montser<PERSON>', 'Segoe UI', Aria<PERSON>, sans-serif;
  font-weight: 700;
  letter-spacing: 1px;
  /* Glass effect */
  background: rgba(30, 34, 90, 0.35);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border-radius: 20px;
  border: 1px solid rgba(255,255,255,0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
}

.hero-small h1 {
  margin-bottom: 15px;
  font-size: 2.8rem;
  font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
  font-weight: 900;
  background: linear-gradient(90deg, #ff8a00 10%, #e52e71 50%, #2575fc 90%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0 4px 16px rgba(37,117,252,0.25), 0 2px 8px rgba(229,46,113,0.18), 0 1px 2px rgba(0,0,0,0.10);
  letter-spacing: 2px;
  animation: heroTitleFadeIn 1s cubic-bezier(0.4,0,0.2,1);
}

@keyframes heroTitleFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.hero-small p {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

.tools-section {
  padding: 60px 0;
}

.category-filter {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.category-button {
  padding: 8px 16px;
  border: 2px solid var(--primary-color);
  border-radius: 20px;
  background: transparent;
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.category-button:hover {
  background: var(--primary-color);
  color: white;
}

.category-button.active {
  background: var(--primary-color);
  color: white;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.card {
  background: white;
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
  /* Glass effect */
  backdrop-filter: blur(10px) saturate(160%);
  -webkit-backdrop-filter: blur(10px) saturate(160%);
  border: 1px solid rgba(255,255,255,0.18);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.card h3 {
  margin-bottom: 15px;
  color: var(--primary-color);
}

.card p {
  margin-bottom: 20px;
  color: var(--muted-text-color);
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-small {
    padding: 40px 0;
  }

  .hero-small h1 {
    font-size: 2rem;
  }

  .hero-small p {
    font-size: 1.1rem;
    padding: 0 15px;
  }

  .tools-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 10px;
  }

  .card {
    padding: 20px;
  }
}


.search-results {
  margin-bottom: 40px;
}

.search-results h2 {
  margin-bottom: 20px;
  color: var(--primary-color);
}

.no-results {
  text-align: center;
  padding: 40px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  grid-column: 1 / -1;
}

.no-results p {
  color: var(--muted-text-color);
  font-size: 1.1rem;
}