:root {
    --section-padding: 70px 0;
}

#hero {
    padding: 130px 0 110px;
    background: linear-gradient(120deg, #1a73e8 0%, #00bfae 100%);
    position: relative;
    overflow: hidden;
    animation: gradientShift 12s ease-in-out infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

#hero::before {
    content: '';
    position: absolute;
    top: -90px;
    right: -90px;
    width: 320px;
    height: 320px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(26, 115, 232, 0.13) 0%, rgba(26, 115, 232, 0) 70%);
    z-index: 0;
}

#hero::after {
    content: '';
    position: absolute;
    bottom: -60px;
    left: -60px;
    width: 220px;
    height: 220px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 191, 174, 0.13) 0%, rgba(0, 191, 174, 0) 70%);
    z-index: 0;
}

.hero-content {
    max-width: 820px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 1;
}

.hero-content h1 {
    font-size: 3.2rem;
    font-weight: 900;
    letter-spacing: 2px;
    background: linear-gradient(90deg, #fff 30%, #e0f7fa 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 18px;
    animation: fadeIn 1.2s;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #e3e8ee;
    margin: 22px 0 44px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.hero-cta {
    display: flex;
    gap: 22px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 36px;
}

.section-title {
    text-align: center;
    margin-bottom: 54px;
    position: relative;
    display: inline-block;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2.2rem;
    font-weight: 800;
    color: #1a73e8;
    letter-spacing: 1.2px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 4px;
    background: linear-gradient(90deg, #1a73e8, #00bfae);
    border-radius: 3px;
}

.tools-grid,
.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 36px;
    margin-top: 44px;
}

.card {
    background: #fff;
    border-radius: 16px;
    padding: 38px 32px;
    box-shadow: 0 10px 32px rgba(26, 115, 232, 0.07);
    transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
    border: 1.5px solid #e3e8ee;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(32px);
    animation: cardPulse 2s ease-in-out infinite;
}

@keyframes cardPulse {
    0% { transform: translateY(32px) scale(1); }
    50% { transform: translateY(32px) scale(1.025); }
    100% { transform: translateY(32px) scale(1); }
}

.card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 18px 38px rgba(0, 191, 174, 0.13);
    border-color: #00bfae33;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #1a73e8, #00bfae);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s;
}

.card:hover::before {
    transform: scaleX(1);
}

.card h3 {
    margin-bottom: 18px;
    font-size: 1.3rem;
    color: #1a73e8;
    font-weight: 700;
}

.card p {
    color: #7b8a99;
    margin-bottom: 22px;
    font-size: 1.05rem;
}

.highlight-bg {
    background: linear-gradient(120deg, #f5f7fa 60%, #e0f7fa 100%);
    position: relative;
    overflow: hidden;
}

.highlight-bg::before {
    content: '';
    position: absolute;
    top: -90px;
    right: -90px;
    width: 220px;
    height: 220px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(26, 115, 232, 0.07) 0%, rgba(26, 115, 232, 0) 70%);
}

.highlight-bg::after {
    content: '';
    position: absolute;
    bottom: -110px;
    left: -110px;
    width: 270px;
    height: 270px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 191, 174, 0.07) 0%, rgba(0, 191, 174, 0) 70%);
}

.value-item {
    text-align: center;
    padding: 36px 24px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 7px 24px rgba(26, 115, 232, 0.05);
    transition: all 0.45s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
    border: 1.5px solid #e3e8ee;
    opacity: 0;
    transform: translateY(32px);
    animation: valueItemFloat 3s ease-in-out infinite;
}

@keyframes valueItemFloat {
    0% { transform: translateY(32px); }
    50% { transform: translateY(26px); }
    100% { transform: translateY(32px); }
}

.value-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 14px 32px rgba(0, 191, 174, 0.10);
}

.value-item h3 {
    margin-bottom: 18px;
    font-size: 1.2rem;
    color: #00bfae;
    font-weight: 700;
}

.value-item p {
    color: #7b8a99;
    font-size: 1.05rem;
}

#cta-final {
    text-align: center;
    background: linear-gradient(120deg, #1a73e8 0%, #00bfae 100%);
    padding: 90px 0;
    margin-top: 48px;
    position: relative;
    overflow: hidden;
}

#cta-final::before {
    content: '';
    position: absolute;
    top: -60px;
    right: -60px;
    width: 170px;
    height: 170px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(26, 115, 232, 0.13) 0%, rgba(26, 115, 232, 0) 70%);
}

#cta-final::after {
    content: '';
    position: absolute;
    bottom: -80px;
    left: -80px;
    width: 220px;
    height: 220px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 191, 174, 0.13) 0%, rgba(0, 191, 174, 0) 70%);
}

#cta-final h2 {
    margin-bottom: 14px;
    font-size: 2.4rem;
    background: linear-gradient(90deg, #fff 30%, #e0f7fa 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
    font-weight: 900;
}

#cta-final p {
    margin-bottom: 34px;
    color: #e3e8ee;
    max-width: 620px;
    margin-left: auto;
    margin-right: auto;
    font-size: 1.15rem;
}

@keyframes slideInFromBottom {
    0% {
        opacity: 0;
        transform: translateY(54px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    opacity: 0;
    transform: translateY(24px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-up {
    opacity: 0;
    transform: translateY(36px);
    transition: opacity 0.8s, transform 0.8s;
}

.slide-up.visible {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-delay-1, .fade-in-delay-2, .fade-in-delay-3, .fade-in {
    opacity: 0;
    transition: opacity 0.6s, transform 0.6s;
}

.fade-in-delay-1.visible, .fade-in-delay-2.visible, .fade-in-delay-3.visible, .fade-in.visible {
    opacity: 1;
}

.card, .value-item {
    opacity: 0;
    transform: translateY(24px);
    transition: opacity 0.5s, transform 0.5s;
}

.card.visible, .value-item.visible {
    opacity: 1;
    transform: translateY(0);
}

@keyframes copyAnimation {
    0% {
        transform: scale(1);
        background-position: 0% 50%;
    }
    50% {
        transform: scale(0.97);
        background-position: 100% 50%;
    }
    100% {
        transform: scale(1);
        background-position: 0% 50%;
    }
}

.copy-animation {
    animation: copyAnimation 0.5s;
    background-size: 200% 200%;
}

.cta-button {
    display: inline-block;
    padding: 14px 36px;
    border: none;
    border-radius: 8px;
    font-weight: 700;
    cursor: pointer;
    background: linear-gradient(90deg, #1a73e8, #00bfae);
    color: #fff;
    box-shadow: 0 4px 18px rgba(26, 115, 232, 0.09);
    font-size: 1.1rem;
    transition: background 0.2s, transform 0.2s, box-shadow 0.2s;
    text-align: center;
    white-space: nowrap;
    text-decoration: none;
}

.cta-button.primary:hover {
    background: linear-gradient(90deg, #00bfae, #1a73e8);
    transform: translateY(-2px) scale(1.04);
    box-shadow: 0 8px 28px rgba(0,191,174,0.13);
}

.cta-button.secondary {
    background: transparent;
    color: #1a73e8;
    border: 2px solid #1a73e8;
}

.cta-button.secondary:hover {
    background: #1a73e8;
    color: #fff;
}

.cta-button.large {
    padding: 18px 44px;
    font-size: 1.18rem;
}

@media (max-width: 768px) {
    #hero {
        padding: 90px 0 70px;
    }
    .hero-content h1 {
        font-size: 2.2rem;
    }
    .hero-subtitle {
        font-size: 1.13rem;
        padding: 0 16px;
    }
    .tools-grid,
    .values-grid {
        grid-template-columns: 1fr;
        gap: 28px;
        padding: 0 12px;
    }
    .section-title {
        margin-bottom: 34px;
        font-size: 1.5rem;
    }
    #cta-final {
        padding: 64px 0;
    }
    #cta-final h2 {
        font-size: 1.5rem;
    }
    #cta-final p {
        font-size: 1rem;
        padding: 0 12px;
    }
}