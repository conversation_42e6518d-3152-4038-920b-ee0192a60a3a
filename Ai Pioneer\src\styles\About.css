.hero-small {
    background: linear-gradient(120deg, #1a73e8 0%, #00bfae 100%);
    padding: 70px 0;
    text-align: center;
    color: #fff;
    font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
    font-weight: 800;
    letter-spacing: 1.5px;
    border-radius: 22px;
    border: 1.5px solid #e3e8ee;
    box-shadow: 0 10px 32px rgba(26, 115, 232, 0.08);
    margin-bottom: 40px;
}

.hero-small h1 {
    margin-bottom: 18px;
    font-size: 2.8rem;
    font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
    font-weight: 900;
    background: linear-gradient(90deg, #fff 30%, #e0f7fa 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: 2px;
    animation: heroTitleFadeIn 1s cubic-bezier(0.4,0,0.2,1);
}

@keyframes heroTitleFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-small p {
    font-size: 1.18rem;
    opacity: 0.92;
    max-width: 600px;
    margin: 0 auto;
}

.about-content {
    padding: 70px 0;
}

.about-section {
    margin-bottom: 64px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 7px 24px rgba(26, 115, 232, 0.07);
    padding: 40px 32px;
    border: 1.5px solid #e3e8ee;
}

.about-section:last-child {
    margin-bottom: 0;
}

.about-section h2 {
    margin-bottom: 32px;
    text-align: center;
    font-size: 2rem;
    font-weight: 800;
    color: #1a73e8;
    letter-spacing: 1.2px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 36px;
    margin-top: 44px;
}

.service-item {
    text-align: center;
    padding: 36px 24px;
    background: #f5f7fa;
    border-radius: 14px;
    box-shadow: 0 4px 16px rgba(0, 191, 174, 0.07);
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1.5px solid #e3e8ee;
}

.service-item:hover {
    transform: translateY(-8px) scale(1.03);
    box-shadow: 0 10px 32px rgba(0,191,174,0.13);
}

.service-item h3 {
    margin-bottom: 16px;
    color: #00bfae;
    font-size: 1.18rem;
    font-weight: 700;
}

.service-item p {
    color: #7b8a99;
    font-size: 1.05rem;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 36px;
    margin-top: 44px;
}

.team-member {
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 4px 16px rgba(26, 115, 232, 0.07);
    padding: 32px 20px 18px 20px;
    text-align: center;
    border: 1.5px solid #e3e8ee;
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 220px;
}

.team-member:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 10px 32px rgba(26, 115, 232, 0.13);
}

.team-member h4 {
    margin-bottom: 10px;
    color: #1a73e8;
    font-size: 1.1rem;
    font-weight: 700;
}

.team-member p {
    color: #7b8a99;
    font-size: 1rem;
}

@media (max-width: 480px) {
    .hero-small {
        padding: 36px 0;
        border-radius: 14px;
    }
    .hero-small h1 {
        font-size: 1.7rem;
        letter-spacing: 1px;
    }
    .hero-small p {
        font-size: 1rem;
        padding: 0 8px;
    }
    .about-content {
        padding: 36px 0;
    }
    .about-section {
        padding: 18px 6px;
        border-radius: 10px;
    }
    .about-section h2 {
        font-size: 1.15rem;
        margin-bottom: 18px;
    }
    .services-grid,
    .team-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    .service-item,
    .team-member {
        padding: 12px 4px;
    }
    .team-member img {
        width: 70px;
        height: 70px;
    }
    .cta-section {
        padding: 22px 0;
    }
    .cta-section h2 {
        font-size: 1.1rem;
    }
    .cta-section p {
        font-size: 0.95rem;
        padding: 0 6px;
    }
    .cta-button.large {
        padding: 12px 18px;
        font-size: 1rem;
    }
}