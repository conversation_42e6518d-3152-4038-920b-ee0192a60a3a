footer {
    background: linear-gradient(90deg, #1a73e8 0%, #00bfae 100%);
    padding: 60px 0 20px;
    margin-top: auto;
    border-top: 2px solid #e3e8ee;
    position: relative;
    overflow: hidden;
    color: #fff;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 48px;
    margin-bottom: 40px;
    position: relative;
    z-index: 1;
}

.footer-col h3 {
    color: #fff;
    font-size: 1.25rem;
    margin-bottom: 18px;
    font-weight: 700;
    letter-spacing: 1px;
    position: relative;
    display: inline-block;
}

.footer-col h3::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #00bfae, #1a73e8);
    border-radius: 2px;
}

.footer-col p {
    color: #e3e8ee;
    font-size: 1rem;
    line-height: 1.7;
}

.footer-col ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-col ul li {
    margin-bottom: 12px;
}

.footer-col ul li a {
    color: #e3e8ee;
    text-decoration: none;
    transition: color 0.2s, transform 0.2s;
    font-size: 1rem;
    display: inline-block;
    padding: 2px 0;
}

.footer-col ul li a:hover {
    color: #00bfae;
    transform: translateX(6px);
}

.copyright {
    text-align: center;
    padding-top: 22px;
    border-top: 1.5px solid #e3e8ee;
    color: #e3e8ee;
    font-size: 0.98rem;
    position: relative;
    z-index: 1;
}

.footer-col {
    opacity: 0;
    transform: translateY(24px);
    transition: opacity 0.5s, transform 0.5s;
}

.footer-col.visible {
    opacity: 1;
    transform: translateY(0);
}

footer::before {
    content: '';
    position: absolute;
    top: -60px;
    right: -60px;
    width: 220px;
    height: 220px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(26, 115, 232, 0.08), rgba(0, 191, 174, 0.08));
    z-index: 0;
}

footer::after {
    content: '';
    position: absolute;
    bottom: -60px;
    left: -60px;
    width: 180px;
    height: 180px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(0, 191, 174, 0.08), rgba(26, 115, 232, 0.08));
    z-index: 0;
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 28px;
        padding: 0 10px;
    }
    .footer-col h3 {
        font-size: 1.1rem;
    }
    .copyright {
        font-size: 0.92rem;
    }
}