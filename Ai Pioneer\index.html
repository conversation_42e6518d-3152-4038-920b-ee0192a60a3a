<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Pioneer - AI Tools, Prompts, News & Glossary</title>
    <meta name="description" content="Discover the best AI tools, prompts, news, and glossary. Stay updated with the latest in artificial intelligence, machine learning, and prompt engineering." />
    <meta name="keywords" content="AI, Artificial Intelligence, AI Tools, AI Prompts, AI News, AI Glossary, Machine Learning, Prompt Engineering, Generative AI, Deep Learning" />
    <meta name="author" content="AI Pioneer" />
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="AI Pioneer - AI Tools, Prompts, News & Glossary" />
    <meta property="og:description" content="Explore trending AI tools, prompts, news, and glossary. Your hub for everything artificial intelligence." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://yourdomain.com/" />
    <meta property="og:image" content="/images/og-image.png" />
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="AI Pioneer - AI Tools, Prompts, News & Glossary" />
    <meta name="twitter:description" content="Discover the best AI tools, prompts, news, and glossary. Stay updated with the latest in artificial intelligence." />
    <meta name="twitter:image" content="/images/og-image.png" />
    <!-- Structured Data -->
    <script type="application/ld+json">{
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "AI Pioneer",
      "url": "https://yourdomain.com/",
      "description": "Discover the best AI tools, prompts, news, and glossary. Stay updated with the latest in artificial intelligence, machine learning, and prompt engineering.",
      "publisher": {
        "@type": "Organization",
        "name": "AI Pioneer"
      }
    }</script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
