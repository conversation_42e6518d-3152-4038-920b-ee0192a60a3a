:root {
    --primary-color: #1a73e8;
    --secondary-color: #00bfae;
    --gradient-start: var(--primary-color);
    --gradient-end: var(--secondary-color);
    --background-color: #f5f7fa;
    --surface-color: #ffffff;
    --text-color: #23272f;
    --heading-color: #181c23;
    --muted-text-color: #7b8a99;
    --border-color: #e3e8ee;
    --white: #fff;
    --black: #000;
}

.sticky-nav {
    position: sticky;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255,255,255,0.92);
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    z-index: 1000;
    box-shadow: 0 2px 16px rgba(26, 115, 232, 0.07);
    border-bottom: 1.5px solid var(--border-color);
    transition: background 0.3s, box-shadow 0.3s, top 0.3s;
}

.sticky-nav.scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.sticky-nav nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
    padding: 0 15px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo {
    font-size: 2rem;
    font-weight: 800;
    letter-spacing: 1.5px;
    color: var(--primary-color);
    text-decoration: none;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: transform 0.2s;
}

.logo:hover {
    transform: scale(1.05);
}

.nav-links {
    display: flex;
    gap: 30px;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-links a {
    color: var(--text-color);
    font-weight: 600;
    position: relative;
    padding: 7px 0;
    text-decoration: none;
    transition: color 0.2s;
    font-size: 1.05rem;
}

.nav-links a:hover,
.nav-links a.active {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
}

.nav-links a:hover::after,
.nav-links a.active::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-bar {
    padding: 10px 35px 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    width: 180px;
    background-color: var(--surface-color);
}

.search-button {
    position: absolute;
    right: 5px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    color: var(--text-color);
    transition: all 0.3s ease;
}

.search-button:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}

.search-bar:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(106, 17, 203, 0.1);
    width: 220px;
}

.cta-button {
    display: inline-block;
    padding: 10px 28px;
    border: none;
    border-radius: 30px;
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.08);
    transition: background 0.2s, transform 0.2s, box-shadow 0.2s;
}

.cta-button:hover {
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
    transform: translateY(-2px) scale(1.03);
    box-shadow: 0 4px 16px rgba(0,191,174,0.13);
}

.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    padding: 7px;
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .mobile-nav-toggle {
        display: block;
    }
    .nav-links {
        display: none;
        position: absolute;
        top: 70px;
        left: 0;
        width: 100%;
        background: var(--surface-color);
        flex-direction: column;
        padding: 25px 0;
        gap: 18px;
        box-shadow: 0 6px 24px rgba(26, 115, 232, 0.07);
        z-index: 1000;
        border-bottom: 1.5px solid var(--border-color);
        animation: slideDown 0.3s;
    }
    .nav-links.active {
        display: flex;
    }
    .nav-actions {
        display: none;
    }
    .sticky-nav nav {
        height: 60px;
    }
    .logo {
        font-size: 1.5rem;
    }
}