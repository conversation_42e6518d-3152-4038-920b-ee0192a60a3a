.hero-small {
  background: linear-gradient(120deg, #1a73e8 0%, #00bfae 100%);
  padding: 70px 0;
  text-align: center;
  color: #fff;
  font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
  font-weight: 800;
  letter-spacing: 1.5px;
  border-radius: 22px;
  border: 1.5px solid #e3e8ee;
  box-shadow: 0 10px 32px rgba(26, 115, 232, 0.08);
  margin-bottom: 40px;
}

.hero-small h1 {
  margin-bottom: 18px;
  font-size: 2.8rem;
  font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
  font-weight: 900;
  background: linear-gradient(90deg, #fff 30%, #e0f7fa 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 2px;
  animation: heroTitleFadeIn 1s cubic-bezier(0.4,0,0.2,1);
}

@keyframes heroTitleFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-small p {
  font-size: 1.18rem;
  opacity: 0.92;
  max-width: 600px;
  margin: 0 auto;
}

.glossary-section {
  padding: 70px 0;
}

.glossary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 36px;
  margin-top: 44px;
}

.term-card {
  background: #fff;
  border-radius: 16px;
  padding: 38px 32px;
  box-shadow: 0 10px 32px rgba(26, 115, 232, 0.07);
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  border: 1.5px solid #e3e8ee;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(32px);
  animation: cardPulse 2s ease-in-out infinite;
}

@keyframes cardPulse {
  0% { transform: translateY(32px) scale(1); }
  50% { transform: translateY(32px) scale(1.025); }
  100% { transform: translateY(32px) scale(1); }
}

.term-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 18px 38px rgba(0, 191, 174, 0.13);
  border-color: #00bfae33;
}

.term-card h3 {
  margin-bottom: 18px;
  font-size: 1.3rem;
  color: #1a73e8;
  font-weight: 700;
}

.term-card p {
  color: #7b8a99;
  margin-bottom: 22px;
  font-size: 1.05rem;
}

.fade-in {
  opacity: 0;
  transform: translateY(24px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

@media (max-width: 768px) {
  .hero-small {
    padding: 44px 0;
    font-size: 1.5rem;
  }
  .glossary-section {
    padding: 36px 0;
  }
  .glossary-grid {
    grid-template-columns: 1fr;
    gap: 22px;
    padding: 0 8px;
  }
  .term-card {
    padding: 24px 10px;
  }
  .term-card h3 {
    font-size: 1.1rem;
  }
}