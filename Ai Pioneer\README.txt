# AI Pioneer

## Description

AI Pioneer is a web application designed to help users discover and master cutting-edge AI tools, prompts, and resources. It aims to enhance productivity and creativity by providing a curated selection of AI resources and expert guidance.

## Features

*   **Home Page:** Introduces the platform and its value proposition with engaging visuals and calls to action.
*   **AI Tools:** A section to explore a curated list of AI tools, likely with descriptions and links.
*   **Prompts:** Browse a collection of prompts for various AI applications.
*   **AI Glossary:** Provides definitions for common AI terms.
*   **AI News:** Displays recent news and updates in the field of artificial intelligence.
*   **About Page:** Information about the AI Pioneer platform.
*   **Responsive Design:** Adapts to different screen sizes for optimal viewing on various devices.
*   **Animations:** Subtle fade-in animations on scroll for a smoother user experience, implemented using Intersection Observer.

## Tech Stack

*   **Frontend Framework:** React
*   **Build Tool:** Vite
*   **Styling:** CSS (with specific stylesheets for each component)
*   **Routing:** React Router (inferred from `Link` usage in `Home.jsx`)

## Getting Started

To run this project locally, follow these steps:

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd Ai Pioneer
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Run the development server:**
    ```bash
    npm run dev
    ```
    This will start the development server, typically at `http://localhost:5173` (check the console output for the exact URL).

4.  **Build for production:**
    ```bash
    npm run build
    ```
    This command bundles the application into static files for production in the `dist` folder.

5.  **Preview the production build:**
    ```bash
    npm run preview
    ```
    This command serves the production build locally for testing.